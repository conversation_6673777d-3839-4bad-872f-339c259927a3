import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
	console.log('New Tab Extension is now active!');

	// 注册创建新标签页的命令
	const createNewTabCommand = vscode.commands.registerCommand('newTabExtension.createNewTab', () => {
		// 创建一个新的无标题文档
		vscode.workspace.openTextDocument({
			content: '// 新建的标签页\n// 在这里开始编写代码...\n',
			language: 'javascript'
		}).then(document => {
			// 在编辑器中显示新文档
			vscode.window.showTextDocument(document);
		});
	});

	// 创建树视图数据提供者
	const treeDataProvider = new NewTabTreeDataProvider();

	// 注册树视图
	const treeView = vscode.window.createTreeView('new-tab-view', {
		treeDataProvider: treeDataProvider,
		showCollapseAll: false
	});

	// 将命令和树视图添加到上下文中
	context.subscriptions.push(createNewTabCommand);
	context.subscriptions.push(treeView);
}

export function deactivate() { }

// 树视图数据提供者类
class NewTabTreeDataProvider implements vscode.TreeDataProvider<NewTabItem> {

	getTreeItem(element: NewTabItem): vscode.TreeItem {
		return element;
	}

	getChildren(element?: NewTabItem): Thenable<NewTabItem[]> {
		if (!element) {
			// 根级别的项目
			return Promise.resolve([
				new NewTabItem('JavaScript 文件', vscode.TreeItemCollapsibleState.None, 'javascript'),
				new NewTabItem('TypeScript 文件', vscode.TreeItemCollapsibleState.None, 'typescript'),
				new NewTabItem('HTML 文件', vscode.TreeItemCollapsibleState.None, 'html'),
				new NewTabItem('CSS 文件', vscode.TreeItemCollapsibleState.None, 'css'),
				new NewTabItem('JSON 文件', vscode.TreeItemCollapsibleState.None, 'json'),
				new NewTabItem('Markdown 文件', vscode.TreeItemCollapsibleState.None, 'markdown'),
				new NewTabItem('纯文本文件', vscode.TreeItemCollapsibleState.None, 'plaintext')
			]);
		}
		return Promise.resolve([]);
	}
}

// 树视图项目类
class NewTabItem extends vscode.TreeItem {
	constructor(
		public readonly label: string,
		public readonly collapsibleState: vscode.TreeItemCollapsibleState,
		public readonly fileType: string
	) {
		super(label, collapsibleState);

		this.tooltip = `创建新的 ${this.label}`;
		this.description = '';

		// 设置点击命令
		this.command = {
			command: 'newTabExtension.createSpecificTab',
			title: 'Create New Tab',
			arguments: [this.fileType]
		};

		// 设置图标
		this.iconPath = new vscode.ThemeIcon('file-add');
	}
}

// 注册创建特定类型文件的命令
vscode.commands.registerCommand('newTabExtension.createSpecificTab', (fileType: string) => {
	let content = '';
	let language = fileType;

	switch (fileType) {
		case 'javascript':
			content = '// JavaScript 文件\nconsole.log("Hello, World!");\n';
			break;
		case 'typescript':
			content = '// TypeScript 文件\nconst message: string = "Hello, World!";\nconsole.log(message);\n';
			break;
		case 'html':
			content = '<!DOCTYPE html>\n<html lang="zh-CN">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>新页面</title>\n</head>\n<body>\n    <h1>Hello, World!</h1>\n</body>\n</html>\n';
			break;
		case 'css':
			content = '/* CSS 样式文件 */\nbody {\n    font-family: Arial, sans-serif;\n    margin: 0;\n    padding: 20px;\n}\n';
			break;
		case 'json':
			content = '{\n    "name": "example",\n    "version": "1.0.0",\n    "description": "示例 JSON 文件"\n}\n';
			break;
		case 'markdown':
			content = '# 标题\n\n这是一个 Markdown 文件。\n\n## 子标题\n\n- 列表项 1\n- 列表项 2\n- 列表项 3\n\n**粗体文本** 和 *斜体文本*\n';
			break;
		default:
			content = '新建的文本文件\n在这里开始编写内容...\n';
			language = 'plaintext';
	}

	vscode.workspace.openTextDocument({
		content: content,
		language: language
	}).then(document => {
		vscode.window.showTextDocument(document);
	});
});
